/* Font Face Declarations */
@font-face {
    font-family: 'HCLTech Roobert';
    src: url('HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'HCLTech Roobert';
    src: url('HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'HCLTech Roobert';
    src: url('HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'HCLTech Roobert';
    src: url('HCLTech Roobert_font/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

/* CSS Custom Properties - Color Palette */
:root {
    /* Primary Colors */
    --primary-teal: #2EC0CB;
    --primary-dark-teal: #23A3AD;
    --primary-blue: #0F5FDC;
    --primary-white: #FFFFFF;

    /* Secondary Colors */
    --secondary-dark-cyan: #17707F;
    --secondary-light-cyan: #36D6D9;
    --secondary-very-light-blue: #A4F4FF;
    --secondary-bright-blue: #3C91FF;
    --secondary-light-blue: #8CC8FA;
    --secondary-very-light-gray-blue: #DCE6F0;

    /* Tertiary Colors */
    --tertiary-dark-navy: #000032;
    --tertiary-light-gray: #ECF3F8;
    --tertiary-black: #000000;

    /* Typography */
    --font-family-primary: 'HCLTech Roobert', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    font-weight: 400;
    line-height: 1.5;
    color: var(--tertiary-black);
    background-color: var(--tertiary-light-gray);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
}

/* Header Styles */
.header {
    background: var(--tertiary-dark-navy);
    color: var(--primary-white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1001;
}

.header-container {
    max-width: 100%;
    margin: 0;
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 50px;
    gap: var(--spacing-sm);
}

/* Header Layout Sections */
.header-left {
    flex: 0 0 auto;
}

.header-center {
    flex: 1;
    max-width: 500px;
    margin: 0 var(--spacing-md);
}

.header-right {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    letter-spacing: -0.02em;
}

.logo-subtitle {
    font-size: 0.75rem;
    font-weight: 300;
    opacity: 0.9;
    margin-top: -0.25rem;
}

/* Advanced Search Bar Styles */
.advanced-search-container {
    position: relative;
    width: 100%;
    max-width: 600px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--primary-white);
    border: 2px solid #e5e7eb;
    border-radius: var(--border-radius-lg);
    transition: var(--transition-normal);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-input-wrapper:hover {
    border-color: var(--primary-teal);
    box-shadow: 0 4px 12px rgba(46, 192, 203, 0.15);
}

.search-input-wrapper:focus-within {
    border-color: var(--primary-teal);
    box-shadow: 0 0 0 4px rgba(46, 192, 203, 0.1), 0 4px 12px rgba(46, 192, 203, 0.15);
}

/* Search Type Selector */
.search-type-selector {
    position: relative;
    flex-shrink: 0;
}

.search-type-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 12px var(--spacing-md);
    background: var(--tertiary-light-gray);
    border: none;
    border-right: 1px solid #e5e7eb;
    color: var(--tertiary-dark-navy);
    font-family: var(--font-family-primary);
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    height: 48px;
    min-width: 80px;
}

.search-type-btn:hover {
    background: var(--secondary-very-light-blue);
    color: var(--primary-teal);
}

.search-type-btn[aria-expanded="true"] {
    background: var(--primary-teal);
    color: var(--primary-white);
}

.search-type-btn[aria-expanded="true"] .search-type-arrow {
    transform: rotate(180deg);
}

.search-type-icon {
    width: 16px;
    height: 16px;
}

.search-type-text {
    font-weight: 600;
}

.search-type-arrow {
    width: 12px;
    height: 12px;
    transition: var(--transition-fast);
}

/* Search Type Dropdown */
.search-type-dropdown {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1010;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    min-width: 180px;
}

.search-type-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-type-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--tertiary-dark-navy);
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition-fast);
    border-left: 3px solid transparent;
}

.search-type-option:hover {
    background: var(--tertiary-light-gray);
    border-left-color: var(--primary-teal);
    color: var(--primary-teal);
}

.search-type-option.active {
    background: var(--secondary-very-light-blue);
    border-left-color: var(--primary-teal);
    color: var(--primary-teal);
    font-weight: 600;
}

.search-type-option svg {
    color: var(--secondary-dark-cyan);
    transition: var(--transition-fast);
}

.search-type-option:hover svg,
.search-type-option.active svg {
    color: var(--primary-teal);
}

/* Advanced Search Input */
.advanced-search-input {
    flex: 1;
    padding: 14px var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--tertiary-black);
    font-family: var(--font-family-primary);
    font-size: 1rem;
    line-height: 1.4;
    outline: none;
    height: 48px;
    box-sizing: border-box;
}

.advanced-search-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* Search Actions */
.search-actions {
    display: flex;
    align-items: center;
    gap: 2px;
    padding-right: 4px;
}

.search-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: none;
    border: none;
    border-radius: var(--border-radius-sm);
    color: #6b7280;
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-action-btn:hover {
    background: var(--tertiary-light-gray);
    color: var(--primary-teal);
    transform: scale(1.05);
}

.search-action-btn:active {
    transform: scale(0.95);
}

.search-submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-teal);
    border: none;
    border-radius: var(--border-radius-md);
    color: var(--primary-white);
    cursor: pointer;
    transition: var(--transition-fast);
    margin-left: 4px;
}

.search-submit-btn:hover {
    background: var(--primary-dark-teal);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(46, 192, 203, 0.3);
}

.search-submit-btn:active {
    transform: scale(0.95);
}

/* Voice Search Animation */
.voice-search-btn.recording {
    background: #ef4444;
    color: var(--primary-white);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
    100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
}

/* Advanced Search Dropdown */
.advanced-search-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 1005;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
    max-height: 500px;
    overflow: hidden;
}

.advanced-search-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Search Status Bar */
.search-status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--tertiary-light-gray);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
}

.search-status-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.search-status-text {
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
    font-weight: 500;
}

.search-results-count {
    background: var(--primary-teal);
    color: var(--primary-white);
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.search-keyboard-shortcuts {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.keyboard-shortcut {
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: 0.75rem;
    color: var(--secondary-dark-cyan);
}

.keyboard-shortcut kbd {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 0.7rem;
    font-family: monospace;
    color: var(--tertiary-dark-navy);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Search Results Container */
.search-results-container {
    max-height: 350px;
    overflow-y: auto;
    padding: var(--spacing-sm) 0;
}

.search-section {
    margin-bottom: var(--spacing-lg);
}

.search-section:last-child {
    margin-bottom: 0;
}

.search-section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin: 0 0 var(--spacing-sm) 0;
    padding: 0 var(--spacing-md);
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--secondary-dark-cyan);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-section-title svg {
    width: 14px;
    height: 14px;
    color: var(--primary-teal);
}

.search-results-list {
    display: flex;
    flex-direction: column;
}

/* Search Result Items */
.search-result-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    color: var(--tertiary-dark-navy);
    text-decoration: none;
    transition: var(--transition-fast);
    border-left: 3px solid transparent;
    position: relative;
}

.search-result-item:hover {
    background: var(--tertiary-light-gray);
    border-left-color: var(--primary-teal);
}

.search-result-item:focus {
    outline: none;
    background: var(--secondary-very-light-blue);
    border-left-color: var(--primary-teal);
}

.result-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    color: var(--secondary-dark-cyan);
    transition: var(--transition-fast);
}

.search-result-item:hover .result-icon {
    background: var(--primary-teal);
    color: var(--primary-white);
    transform: scale(1.05);
}

.result-content {
    flex: 1;
    min-width: 0;
}

.result-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: 2px;
    line-height: 1.3;
}

.result-description {
    font-size: 0.8rem;
    color: var(--secondary-dark-cyan);
    line-height: 1.3;
}

.result-shortcut {
    display: flex;
    align-items: center;
    gap: 2px;
    opacity: 0.7;
    transition: var(--transition-fast);
}

.search-result-item:hover .result-shortcut {
    opacity: 1;
}

.result-shortcut kbd {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 0.7rem;
    font-family: monospace;
    color: var(--tertiary-dark-navy);
}

/* Quick Actions Styling */
.quick-action .result-icon {
    background: linear-gradient(135deg, var(--primary-teal), var(--secondary-light-cyan));
    color: var(--primary-white);
}

.quick-action:hover .result-icon {
    background: linear-gradient(135deg, var(--primary-dark-teal), var(--primary-teal));
    transform: scale(1.1);
}

/* Recent Searches Styling */
.recent-search .result-icon {
    background: var(--secondary-very-light-gray-blue);
}

.recent-search:hover .result-icon {
    background: var(--secondary-bright-blue);
    color: var(--primary-white);
}

/* Search Footer */
.search-dropdown-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--tertiary-light-gray);
    border-top: 1px solid var(--secondary-very-light-gray-blue);
}

.search-footer-left {
    display: flex;
    align-items: center;
}

.search-tip {
    font-size: 0.8rem;
    color: var(--secondary-dark-cyan);
    font-style: italic;
}

.search-footer-right {
    display: flex;
    align-items: center;
}

.advanced-search-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: none;
    border: none;
    color: var(--primary-teal);
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.advanced-search-link:hover {
    background: var(--secondary-very-light-blue);
    color: var(--primary-dark-teal);
}

.advanced-search-link svg {
    width: 12px;
    height: 12px;
}

/* Responsive Design for Advanced Search */
@media (max-width: 768px) {
    .advanced-search-container {
        max-width: 100%;
    }

    .search-input-wrapper {
        border-radius: var(--border-radius-md);
    }

    .search-type-btn {
        min-width: 60px;
        padding: 10px var(--spacing-sm);
    }

    .search-type-text {
        display: none;
    }

    .advanced-search-input {
        font-size: 0.9rem;
        padding: 12px var(--spacing-sm);
    }

    .search-actions {
        gap: 1px;
    }

    .search-action-btn {
        width: 32px;
        height: 32px;
    }

    .search-submit-btn {
        width: 36px;
        height: 36px;
    }

    .advanced-search-dropdown {
        left: -10px;
        right: -10px;
        max-height: 400px;
    }

    .search-status-bar {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .search-keyboard-shortcuts {
        display: none;
    }

    .search-result-item {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .result-icon {
        width: 32px;
        height: 32px;
    }

    .result-title {
        font-size: 0.9rem;
    }

    .result-description {
        font-size: 0.75rem;
    }

    .result-shortcut {
        display: none;
    }

    .search-dropdown-footer {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .header-center {
        margin: 0 var(--spacing-sm);
    }

    .search-input-wrapper {
        height: 44px;
    }

    .search-type-btn {
        height: 44px;
        min-width: 50px;
        padding: 8px var(--spacing-xs);
    }

    .advanced-search-input {
        height: 44px;
        font-size: 0.85rem;
    }

    .search-action-btn {
        width: 30px;
        height: 30px;
    }

    .search-submit-btn {
        width: 34px;
        height: 34px;
    }

    .advanced-search-dropdown {
        left: -15px;
        right: -15px;
    }
}

/* Header Icons and Buttons */
.notification-container,
.profile-container {
    position: relative;
}

.notification-btn,
.profile-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: none;
    border: none;
    color: var(--primary-white);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    transition: var(--transition-normal);
    position: relative;
}

.notification-btn:hover,
.profile-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

.notification-icon,
.profile-icon,
.logout-icon {
    width: 20px;
    height: 20px;
    stroke: currentColor;
}

.dropdown-arrow {
    width: 12px;
    height: 12px;
    stroke: currentColor;
    transition: var(--transition-fast);
}

.profile-btn[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: #ef4444;
    color: var(--primary-white);
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

/* Profile Avatar */
.profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Logout Button */
.logout-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--primary-white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
}

.logout-btn:hover {
    background-color: var(--primary-teal);
    border-color: var(--primary-teal);
    transform: translateY(-1px);
}

/* Dropdown Menus */
.notification-dropdown,
.profile-dropdown {
    position: absolute;
    top: calc(100% + var(--spacing-sm));
    right: 0;
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 320px;
    max-width: 400px;
    z-index: 1002;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
}

.notification-dropdown.active,
.profile-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dropdown-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0;
}

.mark-all-read {
    background: none;
    border: none;
    color: var(--primary-teal);
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.mark-all-read:hover {
    background-color: var(--secondary-very-light-blue);
}

/* Notification List */
.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
    transition: var(--transition-fast);
    position: relative;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background-color: var(--tertiary-light-gray);
}

.notification-item.unread {
    background-color: rgba(46, 192, 203, 0.05);
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background-color: var(--primary-teal);
    border-radius: 50%;
}

.notification-content {
    margin-left: var(--spacing-md);
}

.notification-text {
    font-size: 0.9rem;
    color: var(--tertiary-dark-navy);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--secondary-dark-cyan);
}

.dropdown-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--secondary-very-light-gray-blue);
    text-align: center;
}

.view-all-link {
    color: var(--primary-teal);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.view-all-link:hover {
    text-decoration: underline;
}

/* Profile Dropdown */
.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--secondary-very-light-gray-blue);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-dark-cyan);
}

.user-details {
    flex: 1;
}

.user-name {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0;
}

.user-role {
    font-size: 0.8rem;
    color: var(--secondary-dark-cyan);
    margin: 0;
}

.profile-menu {
    padding: var(--spacing-sm) 0;
}

.profile-menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--tertiary-dark-navy);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition-fast);
}

.profile-menu-item:hover {
    background-color: var(--tertiary-light-gray);
    color: var(--primary-teal);
}

.profile-menu-item svg {
    color: var(--secondary-dark-cyan);
    transition: var(--transition-fast);
}

.profile-menu-item:hover svg {
    color: var(--primary-teal);
}

/* Navigation Bar Styles */
.navigation-bar {
    background: var(--secondary-very-light-gray-blue);
    border-bottom: 1px solid var(--secondary-light-cyan);
    position: sticky;
    top: 60px;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
}

.nav-container {
    max-width: 100%;
    margin: 0;
    padding: 0 var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 45px;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--tertiary-dark-navy);
    transition: var(--transition-fast);
    border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Navigation Styles */
.main-nav {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-sm);
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--tertiary-dark-navy);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    border-radius: var(--border-radius-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-teal);
    transition: var(--transition-normal);
    opacity: 0.1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--primary-teal);
    color: var(--primary-white);
    transform: translateY(-1px);
}

/* Masters dropdown open state indicator */
.nav-link.dropdown-open {
    background-color: var(--primary-teal);
    color: var(--primary-white);
    transform: translateY(-1px);
}

.nav-link.dropdown-open::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid var(--primary-teal);
}

/* Navigation Dropdown Arrow */
.nav-dropdown-arrow {
    width: 12px;
    height: 12px;
    margin-left: var(--spacing-xs);
    transition: var(--transition-fast);
}

.nav-link.dropdown-open .nav-dropdown-arrow {
    transform: rotate(180deg);
}

/* Masters Dropdown */
.nav-item-dropdown {
    position: relative;
}

.masters-dropdown {
    position: absolute;
    top: calc(100% + 2px);
    left: 0;
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 600px;
    max-width: 800px;
    z-index: 1003;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
}

/* Removed automatic hover behavior - now controlled by JavaScript only */
.masters-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.masters-dropdown-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
    background: var(--tertiary-light-gray);
}

.masters-search-container {
    width: 100%;
}

.masters-search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.masters-search-icon {
    position: absolute;
    left: var(--spacing-sm);
    color: #6b7280;
    z-index: 1;
    pointer-events: none;
}

.masters-search-input {
    width: 100%;
    padding: var(--spacing-sm) 2.5rem var(--spacing-sm) 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: var(--border-radius-md);
    background-color: var(--primary-white);
    color: var(--tertiary-black);
    font-family: var(--font-family-primary);
    font-size: 0.9rem;
    transition: var(--transition-normal);
}

.masters-search-input::placeholder {
    color: #6b7280;
}

.masters-search-input:focus {
    outline: none;
    border-color: var(--primary-teal);
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.1);
}

.masters-search-clear {
    position: absolute;
    right: var(--spacing-sm);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.masters-search-clear:hover {
    background-color: #f3f4f6;
    color: var(--tertiary-black);
}

.masters-dropdown-content {
    padding: var(--spacing-md);
}

.masters-dropdown-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xs);
}

.masters-dropdown-item {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--tertiary-dark-navy);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.85rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
}

.masters-dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-teal), var(--secondary-light-cyan));
    color: var(--primary-white);
    border-color: var(--primary-teal);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.masters-item-text {
    display: block;
    line-height: 1.3;
}

.masters-no-results {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--secondary-dark-cyan);
    font-style: italic;
}

/* New Parts Master Styles */
.new-parts-master {
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.new-parts-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-md);
    background: linear-gradient(135deg, var(--tertiary-light-gray) 0%, var(--secondary-very-light-gray-blue) 100%);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
}

.new-parts-title-section h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--tertiary-dark-navy);
    font-size: 1.5rem;
    font-weight: 600;
}

.new-parts-title-section p {
    margin: 0;
    color: var(--secondary-dark-cyan);
    font-size: 0.9rem;
}

.new-parts-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.new-parts-actions .btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.85rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Tab Navigation */
.new-parts-tabs {
    background: var(--primary-white);
}

.tab-navigation {
    display: flex;
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
    background: var(--tertiary-light-gray);
}

.tab-btn {
    background: none;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    font-family: var(--font-family-primary);
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--secondary-dark-cyan);
    cursor: pointer;
    transition: var(--transition-normal);
    border-bottom: 3px solid transparent;
    position: relative;
}

.tab-btn:hover {
    background-color: var(--secondary-very-light-blue);
    color: var(--tertiary-dark-navy);
}

.tab-btn.active {
    background-color: var(--primary-white);
    color: var(--primary-teal);
    border-bottom-color: var(--primary-teal);
}

.tab-content {
    display: none;
    padding: var(--spacing-md);
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

/* Form Styles */
.new-parts-form,
.parts-details-form {
    max-width: 100%;
}

/* Parts Master Styles */
.parts-master {
    max-width: 100%;
}

.parts-header {
    margin-bottom: var(--spacing-xl);
}

.parts-title-section {
    text-align: center;
}

/* Parts Welcome Section - Always visible */
.parts-welcome-section {
    background: linear-gradient(135deg, var(--primary-teal), var(--secondary-light-cyan));
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    color: var(--primary-white);
    text-align: center;
}

.parts-welcome-section .welcome-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.parts-welcome-section .welcome-content h4 {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 var(--spacing-md) 0;
    color: var(--primary-white);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.parts-welcome-section .welcome-content p {
    font-size: 1.1rem;
    margin: 0 0 var(--spacing-xl) 0;
    color: var(--primary-white);
    opacity: 0.95;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Enhanced Welcome Stats Container - 5 items in one line with vibrant colors */
.welcome-stats-container {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: var(--spacing-lg);
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.parts-welcome-section .stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(15px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

/* Add a subtle glow effect */
.parts-welcome-section .stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: var(--border-radius-lg);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.parts-welcome-section .stat-item:hover::before {
    opacity: 1;
}

/* Enhanced vibrant colors for each stat item */
.stat-item.stat-total {
    background: linear-gradient(135deg, rgba(46, 192, 203, 0.8), rgba(54, 214, 217, 0.6));
    border-color: rgba(46, 192, 203, 0.9);
    box-shadow: 0 4px 15px rgba(46, 192, 203, 0.3);
}

.stat-item.stat-new {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.8), rgba(16, 185, 129, 0.6));
    border-color: rgba(34, 197, 94, 0.9);
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.stat-item.stat-salvage {
    background: linear-gradient(135deg, rgba(251, 146, 60, 0.8), rgba(245, 158, 11, 0.6));
    border-color: rgba(251, 146, 60, 0.9);
    box-shadow: 0 4px 15px rgba(251, 146, 60, 0.3);
}

.stat-item.stat-core {
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.8), rgba(139, 92, 246, 0.6));
    border-color: rgba(168, 85, 247, 0.9);
    box-shadow: 0 4px 15px rgba(168, 85, 247, 0.3);
}

.stat-item.stat-exchange {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.8), rgba(248, 113, 113, 0.6));
    border-color: rgba(239, 68, 68, 0.9);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.parts-welcome-section .stat-item:hover {
    transform: translateY(-5px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.9);
    animation: statPulse 2s infinite;
}

/* Enhanced hover effects for each stat type */
.stat-item.stat-total:hover {
    box-shadow: 0 12px 35px rgba(46, 192, 203, 0.5);
    background: linear-gradient(135deg, rgba(46, 192, 203, 0.9), rgba(54, 214, 217, 0.7));
}

.stat-item.stat-new:hover {
    box-shadow: 0 12px 35px rgba(34, 197, 94, 0.5);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.9), rgba(16, 185, 129, 0.7));
}

.stat-item.stat-salvage:hover {
    box-shadow: 0 12px 35px rgba(251, 146, 60, 0.5);
    background: linear-gradient(135deg, rgba(251, 146, 60, 0.9), rgba(245, 158, 11, 0.7));
}

.stat-item.stat-core:hover {
    box-shadow: 0 12px 35px rgba(168, 85, 247, 0.5);
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.9), rgba(139, 92, 246, 0.7));
}

.stat-item.stat-exchange:hover {
    box-shadow: 0 12px 35px rgba(239, 68, 68, 0.5);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(248, 113, 113, 0.7));
}

/* Pulse animation for stat items */
@keyframes statPulse {
    0% { box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3); }
    50% { box-shadow: 0 8px 25px rgba(255, 255, 255, 0.5); }
    100% { box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3); }
}

/* Add floating animation */
@keyframes statFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

.parts-welcome-section .stat-item:nth-child(1) { animation: statFloat 3s ease-in-out infinite; }
.parts-welcome-section .stat-item:nth-child(2) { animation: statFloat 3s ease-in-out infinite 0.5s; }
.parts-welcome-section .stat-item:nth-child(3) { animation: statFloat 3s ease-in-out infinite 1s; }
.parts-welcome-section .stat-item:nth-child(4) { animation: statFloat 3s ease-in-out infinite 1.5s; }
.parts-welcome-section .stat-item:nth-child(5) { animation: statFloat 3s ease-in-out infinite 2s; }

.parts-welcome-section .stat-number {
    display: block;
    font-size: 2.25rem;
    font-weight: 800;
    color: var(--primary-white);
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
    line-height: 1;
}

.parts-welcome-section .stat-label {
    font-size: 0.9rem;
    color: var(--primary-white);
    font-weight: 600;
    opacity: 0.95;
    text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.parts-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.parts-category-card {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.parts-category-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-teal);
}

.category-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.category-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-teal), var(--secondary-light-cyan));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-white);
}

.category-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0 0 var(--spacing-sm) 0;
}

.category-description {
    font-size: 0.9rem;
    color: var(--secondary-dark-cyan);
    margin: 0;
    line-height: 1.4;
}

/* Enhanced Category Actions Layout */
.category-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--tertiary-light-gray);
    border-radius: var(--border-radius-md);
    border-top: 1px solid var(--secondary-very-light-gray-blue);
}

/* Primary action spans full width on top */
.category-add-btn {
    grid-column: 1 / -1;
    grid-row: 1;
    font-size: 0.9rem;
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

/* Secondary actions on bottom row */
.category-view-btn,
.category-import-export-btn {
    grid-row: 2;
    font-size: 0.8rem;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    min-height: 40px;
}

.category-view-btn {
    grid-column: 1;
}

.category-import-export-btn {
    grid-column: 2;
    border-color: var(--primary-teal);
    color: var(--primary-teal);
    background: var(--primary-white);
}

/* Enhanced hover effects */
.category-add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(46, 192, 203, 0.3);
}

.category-view-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.category-import-export-btn:hover {
    background-color: var(--primary-teal);
    color: var(--primary-white);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(46, 192, 203, 0.3);
}

/* Button icons */
.category-actions .btn svg {
    flex-shrink: 0;
}

/* Responsive adjustments for category actions */
@media (max-width: 768px) {
    .category-actions {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm);
    }

    .category-add-btn {
        grid-column: 1;
        grid-row: 1;
        font-size: 0.85rem;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .category-view-btn {
        grid-column: 1;
        grid-row: 2;
    }

    .category-import-export-btn {
        grid-column: 1;
        grid-row: 3;
    }

    .category-view-btn,
    .category-import-export-btn {
        font-size: 0.8rem;
        padding: var(--spacing-sm);
        min-height: 36px;
    }
}

/* Parts Dynamic Content */
.parts-dynamic-content {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    min-height: 400px;
}

.parts-welcome {
    text-align: center;
    padding: var(--spacing-xl) 0;
}

/* General welcome content styling - ensures visibility on any background */
.welcome-content {
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--secondary-very-light-gray-blue);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;
}

/* Ensure welcome content is always readable */
.welcome-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    z-index: -1;
    opacity: 0.95;
}

.welcome-content h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0 0 var(--spacing-md) 0;
}

.welcome-content p {
    font-size: 1rem;
    color: var(--secondary-dark-cyan);
    margin: 0 0 var(--spacing-xl) 0;
    line-height: 1.6;
}

/* High contrast override for welcome content in colored backgrounds */
.parts-welcome-section .welcome-content,
[style*="background"] .welcome-content,
[class*="gradient"] .welcome-content {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.parts-welcome-section .welcome-content h4,
[style*="background"] .welcome-content h4,
[class*="gradient"] .welcome-content h4 {
    color: var(--tertiary-dark-navy) !important;
    text-shadow: none;
}

.parts-welcome-section .welcome-content p,
[style*="background"] .welcome-content p,
[class*="gradient"] .welcome-content p {
    color: var(--secondary-dark-cyan) !important;
    opacity: 1;
    text-shadow: none;
}

.welcome-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-lg);
    max-width: 600px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--tertiary-light-gray);
    border-radius: var(--border-radius-md);
}

.stat-number {
    display: block;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-teal);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--secondary-dark-cyan);
    font-weight: 500;
}

/* Parts Form Container */
.parts-form-container,
.parts-view-container {
    max-width: 100%;
}

.parts-form-header,
.parts-view-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
}

.parts-form-header h4,
.parts-view-header h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0 0 var(--spacing-xs) 0;
}

.parts-form-header p,
.parts-view-header p {
    color: var(--secondary-dark-cyan);
    margin: 0;
}

.view-title-section {
    flex: 1;
}

.view-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.back-to-categories {
    white-space: nowrap;
}

/* View Controls */
.view-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--tertiary-light-gray);
    border-radius: var(--border-radius-md);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.view-mode-selector {
    display: flex;
    gap: var(--spacing-xs);
}

.view-mode-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--secondary-very-light-gray-blue);
    background: var(--primary-white);
    color: var(--secondary-dark-cyan);
    border-radius: var(--border-radius-sm);
    font-size: 0.85rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.view-mode-btn:hover {
    background: var(--secondary-very-light-blue);
    border-color: var(--primary-teal);
}

.view-mode-btn.active {
    background: var(--primary-teal);
    color: var(--primary-white);
    border-color: var(--primary-teal);
}

.search-filter-controls {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.search-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    min-width: 200px;
}

.filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    background: var(--primary-white);
}

/* Parts Data Container */
.parts-data-container {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

/* List View */
.parts-list-view {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th,
.data-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
}

.data-table th {
    background: var(--tertiary-light-gray);
    font-weight: 600;
    color: var(--tertiary-dark-navy);
}

.data-table tbody tr:hover {
    background: var(--tertiary-light-gray);
}

/* Card View */
.parts-card-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-md);
}

.part-card {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    transition: var(--transition-fast);
}

.part-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-teal);
}

.part-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.part-card-header h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0;
    flex: 1;
}

.part-code {
    background: var(--secondary-very-light-blue);
    color: var(--primary-teal);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.part-card-body p {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
}

.part-card-actions {
    display: flex;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--secondary-very-light-gray-blue);
}

/* Compact View */
.parts-compact-view {
    padding: var(--spacing-md);
}

.part-compact-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
    transition: var(--transition-fast);
}

.part-compact-item:hover {
    background: var(--tertiary-light-gray);
}

.part-compact-item:last-child {
    border-bottom: none;
}

.part-compact-info {
    flex: 1;
}

.part-name {
    display: block;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-xs);
}

.part-details {
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
}

.part-compact-actions {
    flex-shrink: 0;
}

/* Button Sizes */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
}

/* Quantity Grid */
.quantity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.quantity-item {
    display: flex;
    flex-direction: column;
}

.quantity-item label {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-xs);
}

.quantity-item.total-stock {
    background: var(--tertiary-light-gray);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 2px solid var(--primary-teal);
}

.quantity-item.total-stock label {
    font-weight: 600;
    color: var(--primary-teal);
}

/* Input with units and currency */
.input-with-unit,
.input-with-currency {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-unit .form-input,
.input-with-currency .form-input {
    padding-right: 50px;
}

.unit,
.currency {
    position: absolute;
    right: var(--spacing-md);
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
    font-weight: 500;
    pointer-events: none;
}

/* Radio and Checkbox Groups - Fixed Duplication Issue */
.radio-group,
.checkbox-group {
    display: flex;
    flex-direction: column;
}

.radio-options,
.checkbox-options {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xs);
}

.radio-label,
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--tertiary-dark-navy);
    position: relative;
    user-select: none;
}

/* Hide native radio buttons completely - Enhanced to prevent duplication */
.radio-label input[type="radio"],
.checkbox-label input[type="checkbox"],
.enhanced-radio-label input[type="radio"],
input[type="radio"] {
    position: absolute !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    pointer-events: none !important;
    visibility: hidden !important;
    z-index: -1 !important;
    left: -9999px !important;
    top: -9999px !important;
}

/* Ensure no native radio button appearance */
input[type="radio"]::-webkit-outer-spin-button,
input[type="radio"]::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
}

input[type="radio"] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

/* Additional browser-specific radio button hiding */
input[type="radio"]::-ms-check {
    display: none !important;
}

input[type="radio"]::-webkit-radio {
    display: none !important;
}

input[type="radio"]::-moz-radio {
    display: none !important;
}

/* Ensure no default radio styling shows through */
.radio-label input[type="radio"],
.enhanced-radio-label input[type="radio"],
.checkbox-label input[type="radio"] {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    color: transparent !important;
    font-size: 0 !important;
    line-height: 0 !important;
    min-height: 0 !important;
    min-width: 0 !important;
    overflow: hidden !important;
}

/* Custom radio button styling */
.radio-custom {
    width: 18px;
    height: 18px;
    border: 2px solid var(--secondary-very-light-gray-blue);
    border-radius: 50%;
    position: relative;
    transition: var(--transition-fast);
    background: var(--primary-white);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Hover state for radio button */
.radio-label:hover .radio-custom {
    border-color: var(--primary-teal);
    box-shadow: 0 0 0 2px rgba(46, 192, 203, 0.1);
}

/* Checked state for radio button */
.radio-label input[type="radio"]:checked + .radio-custom {
    border-color: var(--primary-teal);
    background: var(--primary-teal);
    box-shadow: 0 0 0 2px rgba(46, 192, 203, 0.2);
}

/* Inner dot for checked radio button */
.radio-label input[type="radio"]:checked + .radio-custom::after {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--primary-white);
    border-radius: 50%;
    display: block;
}

/* Focus state for accessibility */
.radio-label input[type="radio"]:focus + .radio-custom,
.enhanced-radio-label input[type="radio"]:focus + .enhanced-radio-custom {
    outline: 2px solid var(--primary-teal);
    outline-offset: 2px;
}

/* Enhanced Radio Button Styling - Prevents Duplication */
.enhanced-radio-label,
.enhanced-radio-group .radio-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--tertiary-dark-navy);
    position: relative;
    user-select: none;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.enhanced-radio-label:hover,
.enhanced-radio-group .radio-label:hover {
    background-color: var(--tertiary-light-gray);
}

.enhanced-radio-label.active,
.enhanced-radio-group .radio-label.active {
    background-color: var(--secondary-very-light-blue);
    border: 1px solid var(--primary-teal);
}

/* Enhanced custom radio button */
.enhanced-radio-custom,
.enhanced-radio-group .radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--secondary-very-light-gray-blue);
    border-radius: 50%;
    position: relative;
    transition: var(--transition-fast);
    background: var(--primary-white);
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced hover state */
.enhanced-radio-label:hover .enhanced-radio-custom,
.enhanced-radio-group .radio-label:hover .radio-custom {
    border-color: var(--primary-teal);
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.1);
    transform: scale(1.05);
}

/* Enhanced checked state */
.enhanced-radio-label input[type="radio"]:checked + .enhanced-radio-custom,
.enhanced-radio-group .radio-label input[type="radio"]:checked + .radio-custom {
    border-color: var(--primary-teal);
    background: var(--primary-teal);
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.2);
}

/* Enhanced inner dot */
.enhanced-radio-label input[type="radio"]:checked + .enhanced-radio-custom .radio-inner,
.enhanced-radio-label input[type="radio"]:checked + .enhanced-radio-custom::after,
.enhanced-radio-group .radio-label input[type="radio"]:checked + .radio-custom::after {
    content: '';
    width: 10px;
    height: 10px;
    background: var(--primary-white);
    border-radius: 50%;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Enhanced text styling */
.enhanced-radio-label .radio-text,
.enhanced-radio-group .radio-label {
    font-weight: 500;
    line-height: 1.4;
}

.enhanced-radio-label .radio-description {
    font-size: 0.8rem;
    color: var(--secondary-dark-cyan);
    margin-top: 2px;
    line-height: 1.3;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.import-export-modal .modal-content {
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
}

.modal-header h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--secondary-dark-cyan);
    cursor: pointer;
    padding: var(--spacing-xs);
    line-height: 1;
}

.modal-close:hover {
    color: var(--tertiary-dark-navy);
}

.modal-body {
    padding: var(--spacing-lg);
}

.import-section,
.export-section {
    margin-bottom: var(--spacing-xl);
}

.import-section h5,
.export-section h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0 0 var(--spacing-md) 0;
}

.import-card {
    background: var(--tertiary-light-gray);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.import-header h6 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0 0 var(--spacing-xs) 0;
}

.import-header p {
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
    margin: 0 0 var(--spacing-md) 0;
}

.import-actions,
.export-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.export-filters {
    background: var(--tertiary-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

/* Draft Management */
.draft-indicator {
    background: var(--secondary-light-cyan);
    color: var(--primary-teal);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: var(--spacing-sm);
}

/* Enhanced Form Actions */
.form-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-start;
    flex-wrap: wrap;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--secondary-very-light-gray-blue);
    margin-top: var(--spacing-xl);
}

.form-actions .btn {
    min-width: 120px;
}

/* Badge Styles */
.category-badge,
.stock-badge,
.movement-badge,
.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Category Badge Colors for Better Visibility */
.category-badge {
    background: var(--primary-teal);
    color: var(--primary-white);
    font-weight: 700;
    border: 1px solid rgba(46, 192, 203, 0.3);
    box-shadow: 0 2px 4px rgba(46, 192, 203, 0.2);
}

/* Category-specific colors for different part types */
.category-badge.category-engine {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    color: var(--primary-white);
    border-color: rgba(37, 99, 235, 0.3);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.category-badge.category-hydraulic {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: var(--primary-white);
    border-color: rgba(220, 38, 38, 0.3);
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

.category-badge.category-electrical {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    color: var(--primary-white);
    border-color: rgba(124, 58, 237, 0.3);
    box-shadow: 0 2px 4px rgba(124, 58, 237, 0.2);
}

.category-badge.category-transmission {
    background: linear-gradient(135deg, #ea580c, #c2410c);
    color: var(--primary-white);
    border-color: rgba(234, 88, 12, 0.3);
    box-shadow: 0 2px 4px rgba(234, 88, 12, 0.2);
}

.category-badge.category-cooling {
    background: linear-gradient(135deg, #0891b2, #0e7490);
    color: var(--primary-white);
    border-color: rgba(8, 145, 178, 0.3);
    box-shadow: 0 2px 4px rgba(8, 145, 178, 0.2);
}

.category-badge.category-fuel {
    background: linear-gradient(135deg, #16a34a, #15803d);
    color: var(--primary-white);
    border-color: rgba(22, 163, 74, 0.3);
    box-shadow: 0 2px 4px rgba(22, 163, 74, 0.2);
}

.category-badge.category-exhaust {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: var(--primary-white);
    border-color: rgba(107, 114, 128, 0.3);
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.2);
}

.category-badge.category-body {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: var(--primary-white);
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
}

.stock-badge.stock-good {
    background: #d4edda;
    color: #155724;
}

.stock-badge.stock-medium {
    background: #fff3cd;
    color: #856404;
}

.stock-badge.stock-low {
    background: #f8d7da;
    color: #721c24;
}

.movement-badge.movement-slow {
    background: #f8d7da;
    color: #721c24;
}

.movement-badge.movement-medium {
    background: #fff3cd;
    color: #856404;
}

.movement-badge.movement-fast {
    background: #d4edda;
    color: #155724;
}

.status-badge.status-active {
    background: #d4edda;
    color: #155724;
}

.status-badge.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Enhanced Part Type Badges for Different Categories */
.part-type-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid transparent;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.part-type-badge.type-new {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: var(--primary-white);
    border-color: rgba(34, 197, 94, 0.3);
}

.part-type-badge.type-salvage {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: var(--primary-white);
    border-color: rgba(245, 158, 11, 0.3);
}

.part-type-badge.type-core {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: var(--primary-white);
    border-color: rgba(139, 92, 246, 0.3);
}

.part-type-badge.type-exchange {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: var(--primary-white);
    border-color: rgba(239, 68, 68, 0.3);
}

/* Enhanced visibility for category badges in tables */
.data-table .category-badge,
.data-table .part-type-badge {
    display: inline-block;
    min-width: 80px;
    text-align: center;
    white-space: nowrap;
}

/* Hover effects for badges */
.category-badge:hover,
.part-type-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Enhanced Card View */
.part-card {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
}

.part-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.part-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
}

.part-card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    flex: 1;
}

.part-badges {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    align-items: flex-end;
}

.part-code {
    background: var(--primary-teal);
    color: var(--primary-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.card-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
}

.info-label {
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
    font-weight: 500;
}

.info-value {
    font-size: 0.9rem;
    color: var(--tertiary-dark-navy);
    font-weight: 600;
}

.part-card-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-start;
    flex-wrap: wrap;
}

/* Part Details Modal */
.part-details-modal .modal-content {
    max-width: 1000px;
    width: 95%;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.details-section {
    background: var(--tertiary-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.details-section h5 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-teal);
    border-bottom: 2px solid var(--primary-teal);
    padding-bottom: var(--spacing-xs);
}

.details-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
}

.details-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
    font-weight: 500;
    flex: 1;
}

.detail-value {
    font-size: 0.9rem;
    color: var(--tertiary-dark-navy);
    font-weight: 600;
    text-align: right;
}

.details-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--secondary-very-light-gray-blue);
}

/* Enhanced Data Table */
.data-table th {
    background: var(--primary-teal);
    color: var(--primary-white);
    font-weight: 600;
    text-align: left;
    padding: var(--spacing-md);
    border: none;
}

.data-table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
    vertical-align: middle;
}

.data-table tr:hover {
    background: var(--tertiary-light-gray);
}

.data-table .btn {
    margin-right: var(--spacing-xs);
}

/* Responsive Design for Forms */
@media (max-width: 768px) {
    .quantity-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--spacing-sm);
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        min-width: auto;
    }

    .radio-options,
    .checkbox-options {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .import-actions,
    .export-actions {
        flex-direction: column;
    }

    .import-actions .btn,
    .export-actions .btn {
        width: 100%;
    }

    .card-info-grid {
        grid-template-columns: 1fr;
    }

    .details-grid {
        grid-template-columns: 1fr;
    }

    .part-card-actions {
        flex-direction: column;
    }

    .part-card-actions .btn {
        width: 100%;
    }

    .details-actions {
        flex-direction: column;
    }

    .details-actions .btn {
        width: 100%;
    }

    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: var(--spacing-sm);
    }
}

.form-sections {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.form-section {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.form-section .section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0 0 var(--spacing-md) 0;
    padding-bottom: var(--spacing-xs);
    border-bottom: 2px solid var(--secondary-very-light-gray-blue);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-xs);
}

.form-input,
.form-select,
.form-textarea {
    padding: var(--spacing-sm);
    border: 1px solid #d1d5db;
    border-radius: var(--border-radius-sm);
    font-family: var(--font-family-primary);
    font-size: 0.9rem;
    color: var(--tertiary-black);
    background-color: var(--primary-white);
    transition: var(--transition-normal);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-teal);
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.1);
}

.form-input:read-only {
    background-color: #f9fafb;
    color: #6b7280;
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Input with Units/Currency */
.input-with-unit,
.input-with-currency {
    display: flex;
    align-items: center;
    border: 1px solid #d1d5db;
    border-radius: var(--border-radius-sm);
    background-color: var(--primary-white);
    transition: var(--transition-normal);
}

.input-with-unit:focus-within,
.input-with-currency:focus-within {
    border-color: var(--primary-teal);
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.1);
}

.input-with-unit .form-input,
.input-with-currency .form-input {
    border: none;
    border-radius: 0;
    flex: 1;
}

.input-with-unit .form-input:focus,
.input-with-currency .form-input:focus {
    box-shadow: none;
}

.unit,
.currency {
    padding: var(--spacing-sm);
    background-color: #f9fafb;
    border-left: 1px solid #d1d5db;
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
    font-weight: 500;
}

/* Duplicate radio styles removed - using the enhanced version above */

/* Quantity Grid */
.quantity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.quantity-item {
    display: flex;
    flex-direction: column;
}

.quantity-item label {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--secondary-dark-cyan);
    margin-bottom: var(--spacing-xs);
}

.quantity-item.total-stock {
    background: linear-gradient(135deg, var(--secondary-very-light-blue), var(--tertiary-light-gray));
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--primary-teal);
}

.quantity-item.total-stock label {
    color: var(--tertiary-dark-navy);
    font-weight: 600;
}

.quantity-item.total-stock .form-input {
    background-color: var(--primary-white);
    font-weight: 600;
    color: var(--primary-teal);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--secondary-very-light-gray-blue);
    margin-top: var(--spacing-xl);
}

/* Stock Management Styles */
.stock-management-section {
    background: var(--primary-white);
}

.stock-overview .section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--secondary-very-light-gray-blue);
}

.stock-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.stock-card {
    background: linear-gradient(135deg, var(--primary-white), var(--tertiary-light-gray));
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition-normal);
}

.stock-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stock-card.warning {
    background: linear-gradient(135deg, #fef3cd, #fde68a);
    border-color: #f59e0b;
}

.stock-card h5 {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--secondary-dark-cyan);
    margin: 0 0 var(--spacing-sm) 0;
}

.stock-value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-teal);
    margin-bottom: var(--spacing-xs);
}

.stock-card.warning .stock-value {
    color: #f59e0b;
}

.stock-unit {
    font-size: 0.8rem;
    color: var(--secondary-dark-cyan);
    font-weight: 500;
}

/* Stock Movement Table */
.stock-movement {
    margin-bottom: var(--spacing-xl);
}

.stock-movement h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-md);
}

.movement-table {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--primary-white);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.data-table th,
.data-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
}

.data-table th {
    background: var(--tertiary-light-gray);
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    font-size: 0.85rem;
}

.data-table td {
    font-size: 0.9rem;
    color: var(--tertiary-black);
}

.movement-in {
    color: #059669;
    font-weight: 600;
}

.movement-out {
    color: #dc2626;
    font-weight: 600;
}

/* Stock Reconciliation */
.stock-reconciliation {
    background: var(--tertiary-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
}

.stock-reconciliation h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-md);
}

/* Import/Export Styles */
.import-export-section {
    background: var(--primary-white);
}

.import-export-section .section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--secondary-very-light-gray-blue);
}

.import-section,
.export-section {
    margin-bottom: var(--spacing-xl);
}

.import-section h5,
.export-section h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-md);
}

.import-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.import-card {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    transition: var(--transition-normal);
}

.import-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.import-header h6 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0 0 var(--spacing-xs) 0;
}

.import-header p {
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
    margin: 0 0 var(--spacing-md) 0;
}

.import-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.import-actions .btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.85rem;
}

/* Import Status */
.import-status {
    background: var(--tertiary-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.import-status h6 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin: 0 0 var(--spacing-md) 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-teal), var(--secondary-light-cyan));
    width: 0%;
    transition: width 0.3s ease;
}

.status-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-xs);
}

.status-details {
    font-size: 0.8rem;
    color: var(--secondary-dark-cyan);
}

/* Export Options */
.export-options {
    background: var(--tertiary-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
}

.export-filters {
    margin-bottom: var(--spacing-lg);
}

.export-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.export-actions .btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Masters dropdown focus styles */
.masters-dropdown-item:focus {
    outline: 2px solid var(--secondary-bright-blue);
    outline-offset: 2px;
    background: linear-gradient(135deg, var(--primary-teal), var(--secondary-light-cyan));
    color: var(--primary-white);
}

/* Responsive Design for Welcome Stats and Dropdowns */
@media (max-width: 1200px) {
    .welcome-stats-container {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md);
        max-width: 800px;
    }

    .masters-dropdown {
        left: -200px;
    }
}

@media (max-width: 900px) {
    .masters-dropdown {
        left: -300px;
        min-width: 500px;
    }
}

/* Mobile responsive design for stats and navigation */
@media (max-width: 768px) {
    .welcome-stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
        max-width: 600px;
    }

    .parts-welcome-section .stat-item {
        padding: var(--spacing-sm);
    }

    .parts-welcome-section .stat-number {
        font-size: 1.5rem;
    }

    .parts-welcome-section .stat-label {
        font-size: 0.75rem;
    }

    .nav-dropdown-arrow {
        display: none;
    }

    .masters-dropdown {
        position: static;
        opacity: 1 !important;
        visibility: visible !important;
        transform: none !important;
        box-shadow: none;
        border: none;
        border-radius: 0;
        background: var(--tertiary-light-gray);
        margin-top: var(--spacing-sm);
        display: none;
    }

    .masters-dropdown.mobile-active {
        display: block;
    }

    .nav-item-dropdown:hover .masters-dropdown {
        display: none;
    }

    .masters-dropdown-header {
        background: var(--primary-white);
        border-radius: var(--border-radius-md);
        margin-bottom: var(--spacing-sm);
    }

    .masters-dropdown-content {
        background: var(--primary-white);
        border-radius: var(--border-radius-md);
    }
}

/* Main Content Styles */
.main-content {
    flex: 1;
    max-width: 100%;
    margin: 0;
    padding: var(--spacing-md) var(--spacing-sm);
    width: 100%;
    box-sizing: border-box;
}

/* Full width utilization for all containers */
.masters-content,
.new-parts-master,
.content-grid,
.status-dashboard,
.options-grid,
.help-grid {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-xs);
    letter-spacing: -0.02em;
}

.section-description {
    font-size: 1rem;
    color: var(--secondary-dark-cyan);
    font-weight: 300;
    max-width: 100%;
    margin: 0;
}

/* Grid Layouts */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
}

/* Card Styles */
.card {
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    border: 1px solid var(--secondary-very-light-gray-blue);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-teal), var(--primary-blue));
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-sm);
}

.card-description {
    color: var(--secondary-dark-cyan);
    margin-bottom: var(--spacing-lg);
    line-height: 1.5;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-md);
    font-family: var(--font-family-primary);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: var(--transition-fast);
}

.btn:hover::before {
    left: 0;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-teal), var(--primary-dark-teal));
    color: var(--primary-white);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-bright-blue), var(--primary-blue));
    color: var(--primary-white);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-tertiary {
    background: linear-gradient(135deg, var(--secondary-light-cyan), var(--secondary-dark-cyan));
    color: var(--primary-white);
}

.btn-tertiary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    color: var(--primary-teal);
    border: 2px solid var(--primary-teal);
}

.btn-outline:hover {
    background: var(--primary-teal);
    color: var(--primary-white);
    transform: translateY(-2px);
}

/* Masters Submenu Styles */
.masters-submenu {
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-xl);
    border: 1px solid var(--secondary-very-light-gray-blue);
    overflow: hidden;
}

.submenu-nav {
    background: linear-gradient(135deg, var(--secondary-very-light-gray-blue) 0%, var(--tertiary-light-gray) 100%);
    padding: var(--spacing-md);
}

.submenu-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-sm);
    list-style: none;
    margin: 0;
    padding: 0;
}

.submenu-item {
    position: relative;
}

.submenu-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--tertiary-dark-navy);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.85rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    background: var(--primary-white);
    border: 1px solid transparent;
}

.submenu-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-teal), var(--secondary-light-cyan));
    transition: var(--transition-normal);
    opacity: 0.1;
}

.submenu-link:hover::before,
.submenu-link.active::before {
    left: 0;
    opacity: 0.2;
}

.submenu-link:hover,
.submenu-link.active {
    background: linear-gradient(135deg, var(--primary-teal), var(--secondary-light-cyan));
    color: var(--primary-white);
    transform: translateY(-1px);
    border-color: var(--primary-teal);
    box-shadow: var(--shadow-sm);
}

/* Masters Content Area */
.masters-content {
    padding: var(--spacing-xl);
}

.submenu-content {
    display: none;
    animation: fadeIn 0.4s ease-in-out;
}

.submenu-content.active {
    display: block;
}

.submenu-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--secondary-very-light-gray-blue);
}

.submenu-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-sm);
    letter-spacing: -0.01em;
}

.submenu-description {
    font-size: 1rem;
    color: var(--secondary-dark-cyan);
    font-weight: 300;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.5;
}

.submenu-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
    margin-top: var(--spacing-lg);
}

/* Status Dashboard Styles */
.status-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
}

.status-card {
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--secondary-very-light-gray-blue);
    transition: var(--transition-normal);
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.status-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-md);
}

.status-indicator {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-sm);
}

.status-good {
    background-color: var(--secondary-light-cyan);
    color: var(--primary-white);
}

.status-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-teal);
    margin-bottom: var(--spacing-sm);
}

.status-details {
    color: var(--secondary-dark-cyan);
    font-size: 0.9rem;
}

/* Options Styles */
.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.option-group {
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--secondary-very-light-gray-blue);
}

.option-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--secondary-very-light-gray-blue);
}

.option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) 0;
}

.option-label {
    font-weight: 500;
    color: var(--tertiary-dark-navy);
}

.option-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-sm);
    font-family: var(--font-family-primary);
    background-color: var(--primary-white);
    color: var(--tertiary-black);
    min-width: 120px;
}

.option-checkbox {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-teal);
}

/* Window Controls */
.window-controls {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* Help Grid */
.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
}

.help-card {
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--secondary-very-light-gray-blue);
    transition: var(--transition-normal);
}

.help-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.help-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-sm);
}

.help-description {
    color: var(--secondary-dark-cyan);
    margin-bottom: var(--spacing-lg);
    line-height: 1.5;
}

/* Footer Styles */
.footer {
    background-color: var(--tertiary-dark-navy);
    color: var(--primary-white);
    margin-top: auto;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg) var(--spacing-md);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-text {
    font-size: 0.9rem;
    opacity: 0.9;
}

.footer-version {
    font-size: 0.8rem;
    opacity: 0.7;
    font-weight: 300;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .main-nav {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--primary-white);
        box-shadow: var(--shadow-md);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-normal);
        border-top: 1px solid var(--secondary-very-light-gray-blue);
    }

    .main-nav.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-list {
        flex-direction: column;
        padding: var(--spacing-md);
        gap: 0;
    }

    .nav-link {
        padding: var(--spacing-md);
        border-radius: 0;
        border-bottom: 1px solid var(--secondary-very-light-gray-blue);
        color: var(--tertiary-dark-navy);
    }

    .nav-link:last-child {
        border-bottom: none;
    }

    .nav-link:hover,
    .nav-link.active {
        background-color: var(--primary-teal);
        color: var(--primary-white);
    }

    .nav-container {
        position: relative;
    }

    .navigation-bar {
        top: 60px;
    }

    /* Header responsive */
    .header-center {
        max-width: 200px;
        margin: 0 var(--spacing-sm);
    }

    .search-input {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-xs) var(--spacing-xs) 2rem;
    }

    .search-icon {
        left: var(--spacing-xs);
        width: 14px;
        height: 14px;
    }

    .header-right {
        gap: var(--spacing-sm);
    }

    .logout-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }

    .logout-btn .logout-icon {
        width: 16px;
        height: 16px;
    }

    .notification-dropdown,
    .profile-dropdown {
        min-width: 280px;
        right: -var(--spacing-sm);
    }

    /* Masters dropdown responsive */
    .masters-dropdown {
        min-width: 320px;
        max-width: 400px;
        left: -100px;
    }

    .masters-dropdown-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
    }

    .masters-dropdown-item {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .masters-content {
        padding: var(--spacing-lg);
    }

    .submenu-actions {
        flex-direction: column;
        align-items: center;
    }

    /* New Parts Master responsive */
    .new-parts-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .new-parts-actions {
        justify-content: center;
    }

    .tab-navigation {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.8rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .quantity-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .stock-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .import-options {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .import-actions,
    .export-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .section-title {
        font-size: 2rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .status-dashboard {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
    }

    .options-grid {
        grid-template-columns: 1fr;
    }

    .help-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .window-controls {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .header-container {
        padding: var(--spacing-sm);
    }

    .nav-container {
        padding: 0 var(--spacing-sm);
    }

    .main-content {
        padding: var(--spacing-sm) var(--spacing-xs);
    }

    .section-title {
        font-size: 1.75rem;
    }

    .card {
        padding: var(--spacing-lg);
    }

    .status-dashboard {
        grid-template-columns: 1fr;
    }

    .logo {
        font-size: 1.4rem;
    }

    .logo-subtitle {
        font-size: 0.65rem;
    }

    .navigation-bar {
        top: 60px;
    }

    /* Mobile header layout */
    .header-container {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .header-left {
        flex: 1;
        min-width: 0;
    }

    .header-center {
        order: 3;
        flex: 1 1 100%;
        max-width: none;
        margin: var(--spacing-sm) 0 0 0;
    }

    .header-right {
        flex: 0 0 auto;
        gap: var(--spacing-xs);
    }

    .logo {
        font-size: 1.2rem;
    }

    .logo-subtitle {
        font-size: 0.6rem;
    }

    .logout-btn span {
        display: none;
    }

    .logout-btn {
        padding: var(--spacing-xs);
        min-width: auto;
    }

    .notification-dropdown,
    .profile-dropdown {
        min-width: 260px;
        right: 0;
        left: auto;
        transform-origin: top right;
    }

    .search-container {
        max-width: none;
    }

    .submenu-title {
        font-size: 1.4rem;
    }

    .submenu-description {
        font-size: 0.9rem;
    }

    .masters-content {
        padding: var(--spacing-md);
    }

    /* New Parts Master mobile */
    .new-parts-header {
        padding: var(--spacing-sm);
    }

    .tab-content {
        padding: var(--spacing-sm);
    }

    .form-section {
        padding: var(--spacing-sm);
    }

    .tab-navigation {
        overflow-x: auto;
        flex-wrap: nowrap;
    }

    .tab-btn {
        white-space: nowrap;
        min-width: 100px;
        flex: none;
    }

    .quantity-grid {
        grid-template-columns: 1fr;
    }

    .stock-cards {
        grid-template-columns: 1fr;
    }

    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .radio-options,
    .checkbox-options {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    /* Mobile Masters dropdown */
    .masters-dropdown {
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        min-width: auto;
        max-width: none;
        border-radius: 0;
        border-left: none;
        border-right: none;
        transform: translateY(-100%);
    }

    .masters-dropdown.show,
    .masters-dropdown.mobile-active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .masters-dropdown-grid {
        grid-template-columns: 1fr;
    }

    .masters-dropdown-item {
        padding: var(--spacing-md);
        border-bottom: 1px solid var(--secondary-very-light-gray-blue);
        border-radius: 0;
    }

    .masters-dropdown-item:last-child {
        border-bottom: none;
    }
}

/* Print Styles */
@media print {
    .header,
    .navigation-bar,
    .footer,
    .mobile-menu-toggle,
    .masters-submenu {
        display: none;
    }

    .main-content {
        max-width: none;
        padding: 0;
    }

    .content-section {
        display: block !important;
        page-break-after: always;
    }

    .content-section:last-child {
        page-break-after: auto;
    }

    .card,
    .status-card,
    .help-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .submenu-content {
        display: block !important;
        page-break-inside: avoid;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles for Accessibility */
.nav-link:focus,
.btn:focus,
.option-select:focus,
.option-checkbox:focus,
.search-input:focus,
.notification-btn:focus,
.profile-btn:focus,
.logout-btn:focus,
.profile-menu-item:focus,
.mark-all-read:focus,
.view-all-link:focus {
    outline: 2px solid var(--secondary-bright-blue);
    outline-offset: 2px;
}

.notification-btn:focus,
.profile-btn:focus,
.logout-btn:focus {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Extra small screens - single column stats */
@media (max-width: 480px) {
    .welcome-stats-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        max-width: 400px;
    }

    .parts-welcome-section .stat-item {
        padding: var(--spacing-md);
    }

    .parts-welcome-section .stat-number {
        font-size: 1.75rem;
    }

    .parts-welcome-section .stat-label {
        font-size: 0.8rem;
    }
}

/* Import/Export Modal Styling */
.import-export-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.import-export-modal.active {
    opacity: 1;
    visibility: visible;
}

.import-export-modal-content {
    background: var(--primary-white);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
    border: 1px solid var(--secondary-very-light-gray-blue);
}

.import-export-modal.active .import-export-modal-content {
    transform: scale(1) translateY(0);
}

/* Desktop Modal Sizing */
@media (min-width: 1024px) {
    .import-export-modal-content {
        width: 800px;
        max-width: 90vw;
        min-height: 600px;
    }
}

/* Tablet Modal Sizing */
@media (min-width: 768px) and (max-width: 1023px) {
    .import-export-modal-content {
        width: 90vw;
        max-width: 700px;
        min-height: 500px;
    }
}

/* Mobile Modal Sizing - Nearly Full Screen */
@media (max-width: 767px) {
    .import-export-modal {
        padding: var(--spacing-sm);
    }

    .import-export-modal-content {
        width: 100%;
        height: 95vh;
        max-height: none;
        border-radius: var(--border-radius-md);
        margin: 0;
    }
}

/* Modal Header */
.import-export-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
    background: linear-gradient(135deg, var(--primary-teal), var(--secondary-light-cyan));
    color: var(--primary-white);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.import-export-modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.import-export-modal-close {
    background: none;
    border: none;
    color: var(--primary-white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.import-export-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Modal Body */
.import-export-modal-body {
    padding: var(--spacing-xl);
}

/* Modal Tabs */
.import-export-tabs {
    display: flex;
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
    margin-bottom: var(--spacing-lg);
    gap: 0;
}

.import-export-tab {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--secondary-dark-cyan);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.9rem;
}

.import-export-tab.active {
    color: var(--primary-teal);
    border-bottom-color: var(--primary-teal);
    background: var(--tertiary-light-gray);
}

.import-export-tab:hover:not(.active) {
    background: var(--secondary-very-light-blue);
    color: var(--primary-teal);
}

/* Tab Content */
.import-export-tab-content {
    display: none;
}

.import-export-tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Mobile Responsive Adjustments */
@media (max-width: 767px) {
    .import-export-modal-header {
        padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
    }

    .import-export-modal-title {
        font-size: 1.25rem;
    }

    .import-export-modal-body {
        padding: var(--spacing-md);
    }

    .import-export-tabs {
        flex-direction: column;
        gap: 0;
    }

    .import-export-tab {
        border-bottom: 1px solid var(--secondary-very-light-gray-blue);
        border-right: none;
        text-align: left;
        padding: var(--spacing-md);
    }

    .import-export-tab.active {
        border-bottom-color: var(--secondary-very-light-gray-blue);
        border-left: 3px solid var(--primary-teal);
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .card,
    .status-card,
    .help-card,
    .option-group {
        border: 2px solid var(--tertiary-black);
    }

    .nav-link:hover,
    .nav-link.active {
        background-color: var(--tertiary-black);
    }

    .import-export-modal-content {
        border: 2px solid var(--tertiary-black);
    }
}

/* Import/Export Content Sections */
.import-export-section {
    margin-bottom: var(--spacing-xl);
}

.import-export-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.import-export-section-description {
    color: var(--secondary-dark-cyan);
    margin-bottom: var(--spacing-lg);
    line-height: 1.5;
}

/* File Upload Area */
.file-upload-area {
    border: 2px dashed var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xl);
    text-align: center;
    background: var(--tertiary-light-gray);
    transition: var(--transition-fast);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.file-upload-area:hover,
.file-upload-area.dragover {
    border-color: var(--primary-teal);
    background: var(--secondary-very-light-blue);
    transform: scale(1.02);
}

.file-upload-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto var(--spacing-md);
    color: var(--primary-teal);
}

.file-upload-text {
    font-size: 1rem;
    font-weight: 500;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-sm);
}

.file-upload-subtext {
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Template Download Section */
.template-download-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.template-card {
    background: var(--primary-white);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition-fast);
    cursor: pointer;
}

.template-card:hover {
    border-color: var(--primary-teal);
    box-shadow: 0 4px 12px rgba(46, 192, 203, 0.15);
    transform: translateY(-2px);
}

.template-icon {
    width: 40px;
    height: 40px;
    margin: 0 auto var(--spacing-md);
    color: var(--primary-teal);
}

.template-title {
    font-weight: 600;
    color: var(--tertiary-dark-navy);
    margin-bottom: var(--spacing-xs);
}

.template-description {
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
    margin-bottom: var(--spacing-md);
}

/* Export Options */
.export-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.export-option {
    background: var(--tertiary-light-gray);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition-fast);
    cursor: pointer;
}

.export-option:hover {
    background: var(--secondary-very-light-blue);
    border-color: var(--primary-teal);
    transform: translateY(-1px);
}

.export-option.selected {
    background: var(--primary-teal);
    color: var(--primary-white);
    border-color: var(--primary-teal);
}

/* Progress Indicator */
.import-progress {
    margin-top: var(--spacing-lg);
    display: none;
}

.import-progress.active {
    display: block;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--secondary-very-light-gray-blue);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-teal), var(--secondary-light-cyan));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    font-size: 0.85rem;
    color: var(--secondary-dark-cyan);
    text-align: center;
}

/* Modal Actions */
.import-export-modal-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--secondary-very-light-gray-blue);
    background: var(--tertiary-light-gray);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Mobile Responsive for Import/Export Content */
@media (max-width: 767px) {
    .template-download-grid,
    .export-options-grid {
        grid-template-columns: 1fr;
    }

    .file-upload-area {
        padding: var(--spacing-lg);
    }

    .file-upload-icon {
        width: 40px;
        height: 40px;
    }

    .import-export-modal-actions {
        flex-direction: column;
        padding: var(--spacing-md);
    }

    .import-export-modal-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* ===== SEARCHABLE DROPDOWN COMPONENT ===== */

/* Searchable Dropdown Container */
.searchable-dropdown {
    position: relative;
    width: 100%;
    font-family: inherit;
}

/* Dropdown Toggle Button */
.searchable-dropdown-toggle {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--secondary-very-light-gray-blue);
    border-radius: var(--border-radius-sm);
    background: var(--primary-white);
    color: var(--tertiary-dark-navy);
    font-size: 0.9rem;
    text-align: left;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 40px;
    position: relative;
}

.searchable-dropdown-toggle:hover {
    border-color: var(--primary-teal);
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.1);
}

.searchable-dropdown-toggle:focus {
    outline: none;
    border-color: var(--primary-teal);
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.2);
}

.searchable-dropdown-toggle.open {
    border-color: var(--primary-teal);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    box-shadow: 0 0 0 3px rgba(46, 192, 203, 0.1);
}

/* Dropdown Toggle Text */
.searchable-dropdown-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--tertiary-dark-navy);
}

.searchable-dropdown-text.placeholder {
    color: var(--secondary-dark-cyan);
    opacity: 0.7;
}

/* Dropdown Toggle Icons */
.searchable-dropdown-icons {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-left: var(--spacing-sm);
}

.searchable-dropdown-clear {
    background: none;
    border: none;
    color: var(--secondary-dark-cyan);
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.searchable-dropdown-clear:hover {
    background: var(--tertiary-light-gray);
    color: var(--tertiary-dark-navy);
}

.searchable-dropdown-arrow {
    color: var(--secondary-dark-cyan);
    transition: transform 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.searchable-dropdown-toggle.open .searchable-dropdown-arrow {
    transform: rotate(180deg);
}

/* Dropdown Menu */
.searchable-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--primary-white);
    border: 1px solid var(--primary-teal);
    border-top: none;
    border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    max-height: 300px;
    overflow: hidden;
    display: none;
}

.searchable-dropdown-menu.open {
    display: block;
    animation: dropdownSlideDown 0.2s ease;
}

@keyframes dropdownSlideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search Input */
.searchable-dropdown-search {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-bottom: 1px solid var(--secondary-very-light-gray-blue);
    background: var(--tertiary-light-gray);
    font-size: 0.9rem;
    width: 100%;
    outline: none;
    color: var(--tertiary-dark-navy);
}

.searchable-dropdown-search::placeholder {
    color: var(--secondary-dark-cyan);
    opacity: 0.7;
}

.searchable-dropdown-search:focus {
    background: var(--primary-white);
    border-bottom-color: var(--primary-teal);
}

/* Options Container */
.searchable-dropdown-options {
    max-height: 240px;
    overflow-y: auto;
    padding: var(--spacing-xs) 0;
}

/* Individual Option */
.searchable-dropdown-option {
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--tertiary-dark-navy);
    font-size: 0.9rem;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    display: flex;
    align-items: center;
    position: relative;
}

.searchable-dropdown-option:hover,
.searchable-dropdown-option.highlighted {
    background: var(--secondary-very-light-blue);
    color: var(--primary-teal);
}

.searchable-dropdown-option.selected {
    background: var(--primary-teal);
    color: var(--primary-white);
    font-weight: 500;
}

.searchable-dropdown-option.selected::after {
    content: '✓';
    position: absolute;
    right: var(--spacing-md);
    font-weight: bold;
}

/* Highlighted Search Text */
.searchable-dropdown-option .highlight {
    background: rgba(46, 192, 203, 0.2);
    color: var(--primary-teal);
    font-weight: 600;
    padding: 1px 2px;
    border-radius: 2px;
}

/* No Results State */
.searchable-dropdown-no-results {
    padding: var(--spacing-lg) var(--spacing-md);
    text-align: center;
    color: var(--secondary-dark-cyan);
    font-style: italic;
    font-size: 0.85rem;
}

/* Loading State */
.searchable-dropdown-loading {
    padding: var(--spacing-lg) var(--spacing-md);
    text-align: center;
    color: var(--secondary-dark-cyan);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.searchable-dropdown-loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid var(--secondary-very-light-gray-blue);
    border-top: 2px solid var(--primary-teal);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Disabled State */
.searchable-dropdown.disabled .searchable-dropdown-toggle {
    background: var(--tertiary-light-gray);
    color: var(--secondary-dark-cyan);
    cursor: not-allowed;
    opacity: 0.6;
}

.searchable-dropdown.disabled .searchable-dropdown-toggle:hover {
    border-color: var(--secondary-very-light-gray-blue);
    box-shadow: none;
}

/* Error State */
.searchable-dropdown.error .searchable-dropdown-toggle {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Mobile Responsive */
@media (max-width: 767px) {
    .searchable-dropdown-menu {
        max-height: 250px;
    }

    .searchable-dropdown-options {
        max-height: 190px;
    }

    .searchable-dropdown-option {
        padding: var(--spacing-md) var(--spacing-md);
        font-size: 1rem;
        min-height: 44px;
    }

    .searchable-dropdown-search {
        padding: var(--spacing-md);
        font-size: 1rem;
    }

    .searchable-dropdown-toggle {
        min-height: 44px;
        padding: var(--spacing-md);
    }
}